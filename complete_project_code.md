# VirusTotal ZIP Scanner - Complete Project Code

This file contains the complete source code for the VirusTotal ZIP Scanner project, including all configuration files, source code, and project structure.

## Project Structure

```
vt-zip-scanner/
├── README.md
├── package.json
├── package-lock.json
├── tsconfig.json
├── tsconfig.app.json
├── tsconfig.node.json
├── vite.config.ts
├── eslint.config.js
├── tailwind.config.js
├── postcss.config.js
├── index.html
├── public/
│   └── shield.svg
└── src/
    ├── main.tsx
    ├── App.tsx
    ├── index.css
    ├── vite-env.d.ts
    ├── types/
    │   └── index.ts
    ├── config/
    │   └── queueConfig.ts
    ├── contexts/
    │   └── ThemeContext.tsx
    ├── components/
    │   ├── ErrorBoundary.tsx
    │   ├── scanner/
    │   │   ├── FileDropzone.tsx
    │   │   ├── TaskCard.tsx
    │   │   ├── QueueSummary.tsx
    │   │   ├── HistoryView.tsx
    │   │   └── history/
    │   │       ├── HistoryActions.tsx
    │   │       ├── HistoryConfirmDialogs.tsx
    │   │       ├── HistoryFilters.tsx
    │   │       ├── HistoryPagination.tsx
    │   │       ├── HistoryStats.tsx
    │   │       └── HistoryTable.tsx
    │   └── ui/
    │       ├── ApiRateLimitIndicator.tsx
    │       ├── Badge.tsx
    │       ├── Button.tsx
    │       ├── Progress.tsx
    │       ├── ThemeToggle.tsx
    │       ├── badge-variants.ts
    │       └── button-variants.ts
    ├── hooks/
    │   ├── useApiRateLimit.ts
    │   ├── useDuplicateDetection.ts
    │   ├── useHistoryActions.ts
    │   ├── useHistoryFilters.ts
    │   ├── useHistoryManager.ts
    │   ├── useHistoryProcessing.ts
    │   ├── useHistorySelection.ts
    │   ├── usePersistedQueue.ts
    │   ├── useProcessingLoop.ts
    │   ├── useQueuePersistence.ts
    │   ├── useQueueProcessing.ts
    │   ├── useQueueState.ts
    │   ├── useSettings.ts
    │   ├── useTaskCompletion.ts
    │   ├── useTaskPolling.ts
    │   ├── useTaskProcessor.ts
    │   └── useTheme.ts
    ├── services/
    │   ├── configService.ts
    │   ├── persistenceOrchestrator.ts
    │   ├── virusTotalClient.ts
    │   ├── virusTotalFactory.ts
    │   ├── virusTotalService.ts
    │   ├── database/
    │   │   ├── baseRepository.ts
    │   │   └── databaseManager.ts
    │   └── repositories/
    │       ├── fileRepository.ts
    │       ├── historyRepository.ts
    │       ├── queueRepository.ts
    │       └── settingsRepository.ts
    ├── utils/
    │   ├── cn.ts
    │   ├── common.ts
    │   ├── errorHandler.ts
    │   ├── logger.ts
    │   ├── queueManager.ts
    │   ├── rateLimiter.ts
    │   ├── secureZipUtils.ts
    │   └── zip/
    │       ├── pathValidator.ts
    │       ├── zipCreator.ts
    │       ├── zipExtractor.ts
    │       ├── zipSecurityAnalyzer.ts
    │       └── zipSecurityConfig.ts
```

## Configuration Files

### package.json

```json
{
  "name": "vt-zip-scanner",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc -b && vite build",
    "lint": "eslint .",
    "preview": "vite preview"
  },
  "dependencies": {
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-toast": "^1.2.14",
    "axios": "^1.9.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "framer-motion": "^12.12.1",
    "jszip": "^3.10.1",
    "lucide-react": "^0.511.0",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-dropzone": "^14.3.8",
    "tailwind-merge": "^3.3.0"
  },
  "devDependencies": {
    "@eslint/js": "^9.25.0",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "@vitejs/plugin-react": "^4.4.1",
    "autoprefixer": "^10.4.21",
    "eslint": "^9.25.0",
    "eslint-plugin-react-hooks": "^5.2.0",
    "eslint-plugin-react-refresh": "^0.4.19",
    "globals": "^16.0.0",
    "postcss": "^8.5.3",
    "tailwindcss": "^3.4.17",
    "typescript": "~5.8.3",
    "typescript-eslint": "^8.30.1",
    "vite": "^6.3.5"
  }
}
```

### tsconfig.json

```json
{
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ]
}
```

### tsconfig.app.json

```json
{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["src"]
}
```

### tsconfig.node.json

```json
{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["vite.config.ts"]
}
```

### vite.config.ts

```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
});
```

### eslint.config.js

```javascript
import js from "@eslint/js";
import globals from "globals";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import tseslint from "typescript-eslint";

export default tseslint.config(
  { ignores: ["dist"] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ["**/*.{ts,tsx}"],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      "react-refresh/only-export-components": [
        "warn",
        { allowConstantExport: true },
      ],
    },
  }
);
```

### tailwind.config.js

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
    },
  },
  plugins: [],
};
```

### postcss.config.js

```javascript
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
```

### index.html

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/shield.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#ffffff" />
    <title>VirusTotal ZIP Scanner</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
```

## Source Code Files

### src/main.tsx

```typescript
import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import { ThemeProvider } from "./contexts/ThemeContext.tsx";
import "./index.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider defaultTheme="system" storageKey="vt-scanner-theme">
      <App />
    </ThemeProvider>
  </React.StrictMode>
);
```

### src/vite-env.d.ts

```typescript
/// <reference types="vite/client" />
```

### src/index.css

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

### src/types/index.ts

```typescript
// File types
export interface FileEntry {
  id: string;
  name: string;
  path: string;
  size: number;
  type: string;
  blob?: Blob;
  sha256?: string; // File hash for duplicate detection
}

// Task states
export type TaskStatus =
  | "pending" // In queue, not yet started
  | "hashing" // Calculating file hash for duplicate detection
  | "uploading" // Uploading to VirusTotal
  | "scanning" // Scanning on VirusTotal
  | "completed" // Scan completed
  | "reused" // Reused existing scan result
  | "error"; // Error occurred

// Queue task
export interface ScanTask {
  id: string;
  file: FileEntry;
  status: TaskStatus;
  progress: number;
  error?: string;
  analysisId?: string;
  report?: AnalysisReport;
  createdAt: Date;
  updatedAt: Date;
}

// VirusTotal API types
export interface AnalysisReport {
  id: string;
  stats: {
    harmless: number;
    malicious: number;
    suspicious: number;
    undetected: number;
    timeout: number;
  };
  results: Record<string, EngineResult>;
  meta: {
    file_info: {
      sha256: string;
      sha1: string;
      md5: string;
      size: number;
      file_type: string;
      filename: string;
    };
  };
}

export interface EngineResult {
  category: string;
  engine_name: string;
  engine_version?: string;
  result?: string;
  method?: string;
  engine_update?: string;
}

// API response types
export interface SubmitFileResponse {
  data: {
    id: string;
    type: string;
  };
}

// Queue context types
export interface QueueContextType {
  tasks: ScanTask[];
  addTask: (file: FileEntry) => void;
  removeTask: (id: string) => void;
  clearQueue: () => void;
  isProcessing: boolean;
  startProcessing: () => void;
  stopProcessing: () => void;
  progress: {
    total: number;
    completed: number;
    percentage: number;
  };
}
```

### src/App.tsx

```typescript
import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Shield, Zap, Info, Settings } from "lucide-react";

import { FileDropzone } from "./components/scanner/FileDropzone";
import { TaskCard } from "./components/scanner/TaskCard";
import { QueueSummary } from "./components/scanner/QueueSummary";
import { Button } from "./components/ui/Button";
import { usePersistedQueue } from "./hooks/usePersistedQueue";
import { HistoryView } from "./components/scanner/HistoryView";
import { ErrorBoundary } from "./components/ErrorBoundary";
import type { FileEntry, ScanTask } from "./types";
import { createSafeZip } from "./utils/secureZipUtils";
import { validateApiKey } from "./services/virusTotalService";
import { generateId } from "./utils/common";
import { ApiRateLimitIndicator } from "./components/ui/ApiRateLimitIndicator";
import { useApiRateLimit } from "./hooks/useApiRateLimit";
import { ThemeToggle } from "./components/ui/ThemeToggle";

function App() {
  const [apiKeyValid, setApiKeyValid] = useState<boolean | null>(null);
  const [hasFiles, setHasFiles] = useState(false);
  const [activeView, setActiveView] = useState<"queue" | "history">("queue");
  const [showSettings, setShowSettings] = useState(false);

  const {
    tasks,
    replaceTasks,
    removeTask,
    clearQueue,
    isProcessing,
    startProcessing,
    stopProcessing,
    progress,
    isInitialized,
    autoStartEnabled,
    updateSettings,
    historyEntries,
    historyTotal,
    historyLoading,
    loadHistory,
    deleteHistoryEntry,
    deleteHistoryEntries,
    clearHistory,
    getHistoryFile,
    getStorageStats,
    rateLimiter,
  } = usePersistedQueue();

  // API Rate Limit tracking using the actual rate limiter from queue processing
  const { rateLimitData } = useApiRateLimit({ rateLimiter });

  // Load storage stats for history view
  const [storageStats, setStorageStats] = useState<
    | {
        historyCount: number;
        filesCount: number;
        estimatedSize?: number;
        actualFileSize?: number;
      }
    | undefined
  >();

  useEffect(() => {
    if (activeView === "history") {
      getStorageStats().then(setStorageStats).catch(console.error);
    }
  }, [activeView, historyEntries, getStorageStats]);

  // Update hasFiles state when tasks change (handles page refresh)
  useEffect(() => {
    const hasTasksNow = tasks.length > 0;

    // Only update hasFiles if we're initialized
    if (isInitialized) {
      setHasFiles(hasTasksNow);
    }
  }, [tasks.length, isInitialized]);

  // Check if API key is valid on mount
  useEffect(() => {
    const checkApiKey = async () => {
      try {
        // Create timeout promise to prevent hanging
        const timeoutPromise = new Promise<boolean>((_, reject) => {
          setTimeout(
            () => reject(new Error("API key validation timeout")),
            3000
          );
        });

        // Race between validation and timeout
        const validationPromise = validateApiKey();
        const isValid = await Promise.race([validationPromise, timeoutPromise]);

        setApiKeyValid(isValid);
      } catch (error) {
        console.warn("API key validation failed or timed out:", error);
        // Set to false so user sees the API key error screen
        setApiKeyValid(false);
      }
    };

    checkApiKey();
  }, []);

  // Handle file extraction from zip
  const handleFilesExtracted = useCallback(
    (files: FileEntry[]) => {
      // Replace existing queue with new files
      replaceTasks(files);
      setHasFiles(true);
      if (autoStartEnabled) {
        startProcessing();
      }
    },
    [replaceTasks, startProcessing, autoStartEnabled]
  );

  // Download a single file
  const handleDownloadFile = useCallback((task: ScanTask) => {
    if (!task.file.blob) return;

    const url = URL.createObjectURL(task.file.blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = task.file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  // Download all safe files
  const handleDownloadAll = useCallback(async () => {
    // Get safe files (completed or reused with no malicious detections)
    const safeFiles = tasks
      .filter(
        (task) =>
          (task.status === "completed" || task.status === "reused") &&
          task.report?.stats.malicious === 0 &&
          task.file.blob
      )
      .map((task) => task.file);

    if (safeFiles.length === 0) return;

    try {
      // Create a new ZIP with safe files
      const safeZip = await createSafeZip(safeFiles);

      // Trigger download
      const url = URL.createObjectURL(safeZip);
      const a = document.createElement("a");
      a.href = url;
      a.download = "safe_files.zip";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error creating safe ZIP:", error);
      alert("Failed to create ZIP file with safe files");
    }
  }, [tasks]);

  // Reset the app state
  const handleReset = useCallback(() => {
    clearQueue();
    setHasFiles(false);
  }, [clearQueue]);

  // Add history download handler
  const handleHistoryDownload = useCallback(
    async (fileId: string, fileName: string) => {
      const blob = await getHistoryFile(fileId);
      if (!blob) {
        alert("File not found in history");
        return;
      }

      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    },
    [getHistoryFile]
  );

  // Add bulk history download handler
  const handleHistoryBulkDownload = useCallback(
    async (entryIds: string[]) => {
      if (entryIds.length === 0) return;

      try {
        // Get all the files for the selected entries
        const fileEntries: { fileName: string; blob: Blob }[] = [];

        for (const entryId of entryIds) {
          const entry = historyEntries.find((e) => e.id === entryId);
          if (!entry) continue;

          const blob = await getHistoryFile(entry.fileId);
          if (blob) {
            fileEntries.push({
              fileName: entry.fileName,
              blob: blob,
            });
          }
        }

        if (fileEntries.length === 0) {
          alert("No files found for download");
          return;
        }

        if (fileEntries.length === 1) {
          // Single file - download directly
          const { fileName, blob } = fileEntries[0];
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } else {
          // Multiple files - create ZIP
          const { createSafeZip } = await import("./utils/secureZipUtils");

          // Convert to FileEntry format for createSafeZip
          const files = fileEntries.map(({ fileName, blob }) => ({
            id: generateId(),
            name: fileName,
            path: fileName,
            size: blob.size,
            type: blob.type || "application/octet-stream",
            blob: blob,
          }));

          const safeZip = await createSafeZip(files);

          // Trigger download
          const url = URL.createObjectURL(safeZip);
          const a = document.createElement("a");
          a.href = url;
          a.download = `safe_files_${
            new Date().toISOString().split("T")[0]
          }.zip`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      } catch (error) {
        console.error("Error downloading selected files:", error);
        alert("Failed to download selected files");
      }
    },
    [historyEntries, getHistoryFile]
  );

  // Toggle auto-start setting
  const handleToggleAutoStart = useCallback(async () => {
    await updateSettings({ autoStartScanning: !autoStartEnabled });
  }, [autoStartEnabled, updateSettings]);

  // If API key check hasn't completed yet or not initialized
  if (apiKeyValid === null || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center p-8">
          <div className="h-12 w-12 rounded-full border-4 border-primary border-t-transparent animate-spin mb-4" />
          <p className="text-lg">Initializing scanner...</p>
        </div>
      </div>
    );
  }

  // If API key is not valid
  if (apiKeyValid === false) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="max-w-md w-full p-6 bg-card rounded-lg shadow-lg">
          <div className="flex flex-col items-center text-center">
            <Shield className="h-16 w-16 text-destructive mb-4" />
            <h1 className="text-2xl font-bold mb-2">API Key Error</h1>
            <p className="text-muted-foreground mb-6">
              Your VirusTotal API key is missing or invalid. Please set a valid
              API key in your environment variables.
            </p>
            <div className="bg-muted p-4 rounded-md text-left w-full mb-4">
              <p className="font-mono text-sm">
                VITE_VT_API_KEY=your_api_key_here
              </p>
            </div>
            <p className="text-sm text-muted-foreground">
              You can get a free API key by signing up at{" "}
              <a
                href="https://www.virustotal.com/gui/join-us"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                VirusTotal.com
              </a>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <Shield className="h-6 w-6 text-primary mr-2" />
            <h1 className="text-xl font-bold">VirusTotal ZIP Scanner</h1>
          </div>

          <div className="flex items-center gap-4">
            {/* Navigation tabs */}
            <div className="flex gap-2">
              <Button
                variant={activeView === "queue" ? "default" : "ghost"}
                onClick={() => setActiveView("queue")}
              >
                Queue
              </Button>
              <Button
                variant={activeView === "history" ? "default" : "ghost"}
                onClick={() => setActiveView("history")}
              >
                History
              </Button>
            </div>

            {/* API Rate Limit Indicator */}
            <ApiRateLimitIndicator
              rateLimitData={rateLimitData}
              compact={true}
              className="hidden sm:flex"
            />

            {/* Theme Toggle */}
            <ThemeToggle />

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>

            {hasFiles && activeView === "queue" && (
              <Button variant="ghost" onClick={handleReset}>
                Upload New ZIP
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b bg-muted/30 overflow-hidden"
          >
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-start justify-between gap-6">
                <div className="flex-1">
                  <h3 className="text-lg font-medium mb-4">Settings</h3>
                  <div className="space-y-4">
                    <label className="flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        checked={autoStartEnabled}
                        onChange={handleToggleAutoStart}
                        className="rounded"
                      />
                      Auto-start scanning when files are uploaded
                    </label>

                    <div className="flex items-center gap-2 text-sm">
                      <span>Theme:</span>
                      <ThemeToggle variant="dropdown" />
                    </div>
                  </div>
                </div>

                {/* Detailed API Rate Limit Info */}
                <div className="w-80">
                  <ApiRateLimitIndicator
                    rateLimitData={rateLimitData}
                    compact={false}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <main className="container mx-auto px-4 py-8">
        {activeView === "queue" ? (
          !hasFiles ? (
            <div key="upload">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-2">Scan a ZIP file</h2>
                <p className="text-muted-foreground mb-4">
                  Upload a ZIP file to scan all contained files with VirusTotal.
                </p>
              </div>

              <FileDropzone onFilesExtracted={handleFilesExtracted} />

              <div className="mt-8 max-w-2xl mx-auto">
                <div className="bg-muted rounded-lg p-4 flex items-start">
                  <Info className="h-5 w-5 text-primary mr-3 flex-shrink-0 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Rate Limits Apply</p>
                    <p className="text-muted-foreground mt-1">
                      VirusTotal public API is limited to 4 requests per minute
                      and 500 per day. Files will be processed in queue
                      respecting these limits.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div key="results">
              <ErrorBoundary
                title="Queue Error"
                description="There was an error with the scanning queue. This might be due to a large number of files or a processing issue."
              >
                <QueueSummary
                  tasks={tasks}
                  progress={progress}
                  isProcessing={isProcessing}
                  onStartProcessing={startProcessing}
                  onStopProcessing={stopProcessing}
                  onClearQueue={clearQueue}
                  onDownloadAll={handleDownloadAll}
                />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-medium">
                      Files ({tasks.length})
                    </h2>

                    <div className="flex items-center text-sm text-muted-foreground">
                      <Zap className="h-4 w-4 mr-1 text-yellow-500" />
                      <span>Powered by VirusTotal API</span>
                    </div>
                  </div>

                  {tasks.map((task) => (
                    <TaskCard
                      key={task.id}
                      task={task}
                      onRemove={removeTask}
                      onDownload={handleDownloadFile}
                    />
                  ))}
                </div>
              </ErrorBoundary>
            </div>
          )
        ) : (
          <div key="history">
            <HistoryView
              entries={historyEntries}
              total={historyTotal}
              loading={historyLoading}
              onLoadHistory={loadHistory}
              onDeleteHistoryEntry={deleteHistoryEntry}
              onDeleteHistoryEntries={deleteHistoryEntries}
              onClearHistory={clearHistory}
              onDownloadFile={handleHistoryDownload}
              onDownloadSelectedFiles={handleHistoryBulkDownload}
              storageStats={storageStats}
            />
          </div>
        )}
      </main>

      <footer className="border-t mt-auto">
        <div className="container mx-auto px-4 py-4">
          <p className="text-sm text-muted-foreground text-center">
            VirusTotal ZIP Scanner • Built with React & Vite
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
```

## Configuration Files (continued)

### src/config/queueConfig.ts

```typescript
/**
 * Configuration constants for queue processing
 */

// VirusTotal rate limit: 4 requests per minute (optimized timing)
export const RATE_LIMIT_CONFIG = {
  REQUEST_LIMIT: 4,
  REQUEST_WINDOW: 60 * 1000, // 60 seconds
  POLL_INTERVAL: 20000, // 20 seconds
  RATE_LIMITED_POLL_INTERVAL: 60000, // 1 minute
  BATCH_SUBMIT_DELAY: 2000, // 2 seconds
  MIN_REQUEST_SPACING: 18000, // 18 seconds
} as const;

// Auto-save configuration
export const SAVE_CONFIG = {
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  SINGLE_FILE_SAVE_INTERVAL: 15000, // 15 seconds
  IMMEDIATE_SAVE_DELAY: 1000, // 1 second
} as const;

// Scan timeout configuration
export const SCAN_CONFIG = {
  MAX_SCAN_TIME: 10 * 60 * 1000, // 10 minutes
} as const;
```

### src/services/configService.ts

```typescript
/**
 * Configuration service for managing application settings
 */

interface VirusTotalConfig {
  apiKey: string;
  apiUrl: string;
  timeout: number;
}

interface AppConfig {
  virusTotal: VirusTotalConfig;
  app: {
    name: string;
    version: string;
  };
}

/**
 * Singleton configuration service
 */
class ConfigService {
  private static instance: ConfigService;
  private config: AppConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * Get VirusTotal configuration
   */
  getVirusTotalConfig(): VirusTotalConfig {
    return this.config.virusTotal;
  }

  /**
   * Get application configuration
   */
  getAppConfig() {
    return this.config.app;
  }

  /**
   * Update VirusTotal API key (for runtime configuration)
   */
  setVirusTotalApiKey(apiKey: string): void {
    this.config.virusTotal.apiKey = apiKey;
  }

  /**
   * Load configuration from environment variables
   */
  private loadConfig(): AppConfig {
    const apiKey = import.meta.env.VITE_VT_API_KEY || "";

    if (!apiKey) {
      console.warn("VirusTotal API key not found in environment variables");
    }

    return {
      virusTotal: {
        apiKey,
        apiUrl: "https://www.virustotal.com/api/v3",
        timeout: 30000, // 30 seconds
      },
      app: {
        name: "VirusTotal ZIP Scanner",
        version: "1.0.0",
      },
    };
  }
}

// Export singleton instance
export const configService = ConfigService.getInstance();
```

## Note on Complete Codebase

This file contains the core structure and main files of the VirusTotal ZIP Scanner project. Due to file size limitations, the complete codebase includes many additional files:

### Additional Component Files

- `src/components/ErrorBoundary.tsx` - Error boundary component for graceful error handling
- `src/components/scanner/FileDropzone.tsx` - File upload and ZIP processing component
- `src/components/scanner/TaskCard.tsx` - Individual scan task display component
- `src/components/scanner/QueueSummary.tsx` - Queue overview and controls component
- `src/components/scanner/HistoryView.tsx` - Scan history management component
- `src/components/scanner/history/` - History-related sub-components (filters, actions, table, etc.)
- `src/components/ui/` - Reusable UI components (Button, Badge, Progress, etc.)

### Hook Files

- `src/hooks/usePersistedQueue.ts` - Main queue management hook
- `src/hooks/useApiRateLimit.ts` - API rate limiting hook
- `src/hooks/useTheme.ts` - Theme management hook
- `src/hooks/useDuplicateDetection.ts` - File deduplication hook
- `src/hooks/useHistoryManager.ts` - History management hook
- And many more specialized hooks for queue processing, task management, etc.

### Service Files

- `src/services/virusTotalService.ts` - VirusTotal API integration
- `src/services/virusTotalClient.ts` - HTTP client for VirusTotal API
- `src/services/virusTotalFactory.ts` - Service factory pattern
- `src/services/persistenceOrchestrator.ts` - Data persistence coordination
- `src/services/database/` - IndexedDB management
- `src/services/repositories/` - Data access layer

### Utility Files

- `src/utils/secureZipUtils.ts` - Secure ZIP file processing
- `src/utils/rateLimiter.ts` - Rate limiting utilities
- `src/utils/common.ts` - Common utility functions
- `src/utils/logger.ts` - Logging utilities
- `src/utils/zip/` - ZIP processing utilities (extraction, security, validation)

### Context Files

- `src/contexts/ThemeContext.tsx` - Theme provider and context

### Key Features Implemented

1. **Security-First ZIP Processing**

   - ZIP bomb detection and prevention
   - Path traversal attack prevention
   - File type validation
   - Size limit enforcement

2. **VirusTotal Integration**

   - File upload and scanning
   - Rate limit compliance (4 requests/minute)
   - Duplicate detection to save API quota
   - Real-time scan result monitoring

3. **Advanced Queue Management**

   - Persistent queue storage using IndexedDB
   - Auto-save functionality
   - Background processing
   - Error handling and retry logic

4. **User Interface**

   - Responsive design with Tailwind CSS
   - Dark/light/system theme support
   - Real-time progress tracking
   - Comprehensive scan history

5. **Data Management**
   - IndexedDB for offline storage
   - Repository pattern for data access
   - Automatic cleanup and retention policies
   - Bulk operations support

The complete project represents a production-ready application with enterprise-level architecture, comprehensive security measures, and a polished user experience. All source files follow TypeScript best practices and include proper error handling, logging, and documentation.

To access the complete codebase, please refer to the actual project files in the repository structure outlined at the beginning of this document.
